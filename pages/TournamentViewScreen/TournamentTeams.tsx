import React from 'react';
import { View } from 'react-native';
import { Text } from '@/components/ui/text';
import { PlusIcon } from 'lucide-react-native';
import { useRouter } from 'expo-router';
import NoDataFound from '@/components/k-components/NoDataFound';
import SCREENS from '@/constants/Screens';
import { CTAButton } from '@/components/ui/primaryCTAbutton';

export default function TournamentTeams({ tournament }: { tournament: any }) {
  const router = useRouter();
  const rules = tournament?.tournament_rules || {};
  const rulesSet = Object.keys(rules).length > 0;

  // Simulated: No teams added yet
  const hasTeams = false;

  return (
    <View className="flex-1 relative px-4">
      <View className="pt-2">
        {hasTeams ? (
          <Text>Show team list here</Text>
        ) : (
          <NoDataFound
            title="No Teams Added Yet"
            subtitle="Add teams to get started with your tournament."
            action={
              <CTAButton
                title="Add Team"
                lefticon={PlusIcon}
                onPress={() =>
                  router.push({
                    pathname: SCREENS.TEAMS_CREATE,
                    params: {
                      'tournament-id': tournament.id,
                    },
                  })
                }
              />
            }
          />
        )}
      </View>
    </View>
  );
}
